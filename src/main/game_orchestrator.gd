class_name GameOrchestrator
extends Node

@export var hub_scene: PackedScene
@export var run_levels: Array[PackedScene]
@export var player_scene: PackedScene

var _scenes_container: Node
var _current_scene_instance: Node
var _current_level_index: int = -1
var _run_state_service: RunStateService

func initialize(scenes_container: Node) -> void:
	self._scenes_container = scenes_container

func start_run() -> void:
	_on_start_run()

func go_to_hub() -> void:
	_cleanup_run_state_service()
	go_to_scene(hub_scene)

func go_to_scene(scene_resource: PackedScene) -> void:
	GameStateService.set_state(GameStateService.GameState.TRANSITIONING)
	await _remove_current_scene()
	_add_new_scene(scene_resource)

func _remove_current_scene() -> void:
	if not is_instance_valid(_current_scene_instance):
		return

	_disconnect_current_scene_signals()
	_current_scene_instance.queue_free()
	await _current_scene_instance.tree_exited

func _add_new_scene(scene_resource: PackedScene) -> void:
	var new_scene: Node = scene_resource.instantiate()
	_scenes_container.add_child(new_scene)
	_current_scene_instance = new_scene

	if _current_scene_instance is BaseLevel:
		var base_level: BaseLevel = _current_scene_instance as BaseLevel
		base_level.ready_for_spawn.connect(_on_scene_ready_for_spawn.bind(base_level), CONNECT_ONE_SHOT)

	_connect_current_scene_signals()

func _on_scene_ready_for_spawn(scene: BaseLevel) -> void:
	scene.prepare_scene()
	scene.setup_and_spawn_player(player_scene)
	if scene is Level:
		GameStateService.set_state(GameStateService.GameState.IN_LEVEL)
	elif scene is Hub:
		GameStateService.set_state(GameStateService.GameState.HUB)

func _connect_current_scene_signals() -> void:
	if _current_scene_instance is Level:
		var level_instance: Level = _current_scene_instance as Level
		level_instance.level_completed.connect(_on_level_completed)
		level_instance.level_failed.connect(_on_level_failed)
	elif _current_scene_instance is Hub:
		var hub_instance: Hub = _current_scene_instance as Hub
		hub_instance.start_run.connect(_on_start_run)

func _disconnect_current_scene_signals() -> void:
	if _current_scene_instance is Level:
		var level_instance: Level = _current_scene_instance as Level
		level_instance.level_completed.disconnect(_on_level_completed)
		level_instance.level_failed.disconnect(_on_level_failed)
	elif _current_scene_instance is Hub:
		var hub_instance: Hub = _current_scene_instance as Hub
		hub_instance.start_run.disconnect(_on_start_run)

func _on_start_run() -> void:
	_current_level_index = 0
	_create_run_state_service()
	if not run_levels.is_empty():
		go_to_scene(run_levels[_current_level_index])
	else:
		push_error("Нет уровней для запуска!")
		go_to_hub()

func _on_level_completed() -> void:
	_collect_level_data()
	_current_level_index += 1
	if _current_level_index < run_levels.size():
		go_to_scene(run_levels[_current_level_index])
	else:
		_convert_run_currency()
		go_to_hub()

func _on_level_failed() -> void:
	go_to_hub()

func _create_run_state_service() -> void:
	_run_state_service = RunStateService.new()
	add_child(_run_state_service)
	_run_state_service.reset()

func _collect_level_data() -> void:
	if _current_scene_instance is Level:
		var level_instance: Level = _current_scene_instance as Level
		var level_state_component: LevelStateComponent = level_instance.level_state_component
		var painted_tiles: int = level_state_component.data.painted_tiles
		_run_state_service.add_painted_tiles(painted_tiles)

func _convert_run_currency() -> void:
	var total_painted_tiles: int = _run_state_service.get_total_painted_tiles()
	if total_painted_tiles > 0:
		GameProgress.add_meta_currency(total_painted_tiles)

func _cleanup_run_state_service() -> void:
	_run_state_service.queue_free()
	_run_state_service = null
