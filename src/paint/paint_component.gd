class_name PaintComponent
extends Node

signal paint_changed(current_paint: int, max_paint: int)

@export var data: PaintData

var current_paint: int:
	set(new_value):
		if not is_instance_valid(data):
			return
		var clamped_value: int = clamp(new_value, 0, data.max_paint)
		if current_paint == clamped_value:
			return
		current_paint = clamped_value
		paint_changed.emit(current_paint, data.max_paint)

func _ready() -> void:
	if is_instance_valid(data):
		current_paint = data.max_paint

func reset() -> void:
	if is_instance_valid(data):
		current_paint = data.max_paint
