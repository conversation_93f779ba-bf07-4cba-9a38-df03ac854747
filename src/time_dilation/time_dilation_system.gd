class_name TimeDilationSystem
extends Node

@export var tile_query_system: TileQuerySystem
@export var player_service: PlayerService

var _player_movement_component: MovementComponent
var _player_ability_component: AbilityComponent

func _ready() -> void:
	player_service.player_initialized.connect(_on_player_initialized)

func _on_player_initialized(_player_node: Node2D) -> void:
	_player_movement_component = player_service.get_movement_component()
	_player_ability_component = player_service.get_ability_component()

	_player_movement_component.data.movement_started.connect(_on_player_movement_started)
	_player_movement_component.data.movement_completed.connect(_on_player_movement_completed)

	_set_time_state(false)

func _on_player_movement_started(_direction: Vector2) -> void:
	var actor: Node2D = _player_movement_component.actor
	var parent_node: Node2D = actor.get_parent() as Node2D
	var global_target_pos: Vector2 = parent_node.to_global(_player_movement_component.target_position)

	var target_tile: Node2D = tile_query_system.get_tile_at_global_pos(global_target_pos)
	var should_speed_up: bool = true
	if is_instance_valid(target_tile) and target_tile is ColorTile and (target_tile as ColorTile).is_painted() and _player_has_ability(MaintainNormalTimeAbilityData):
		should_speed_up = false

	_set_time_state(should_speed_up)

func _on_player_movement_completed() -> void:
	_set_time_state(false)

func _player_has_ability(ability_type: GDScript) -> bool:
	if not is_instance_valid(_player_ability_component):
		return false
	return _player_ability_component.has_ability(ability_type)

func _set_time_state(is_player_moving: bool) -> void:
	var components: Array[Node] = get_tree().get_nodes_in_group(&"time_sensitive")
	for node in components:
		var component: TimeSensitiveComponent = node as TimeSensitiveComponent
		if not is_instance_valid(component):
			continue
		if is_player_moving:
			component.current_speed = component.data.normal_speed
			component.current_rotation_speed = component.data.normal_rotation_speed
		else:
			component.current_speed = component.data.slow_speed
			component.current_rotation_speed = component.data.slow_rotation_speed
