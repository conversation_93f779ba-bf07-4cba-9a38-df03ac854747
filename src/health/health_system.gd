class_name HealthSystem
extends Node

func set_health(component: HealthComponent, value: int) -> void:
	if not is_instance_valid(component) or not is_instance_valid(component.data):
		return

	var v: int = value
	if v < 0:
		v = 0
	if v > component.data.max_health:
		v = component.data.max_health
	component.current_health = v

func apply_damage(component: HealthComponent, amount: int) -> void:
	set_health(component, component.current_health - amount)

func heal(component: HealthComponent, amount: int) -> void:
	set_health(component, component.current_health + amount)

func is_dead(component: HealthComponent) -> bool:
	return component.current_health <= 0
