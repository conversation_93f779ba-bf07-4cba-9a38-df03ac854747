class_name PlayerService
extends Node

signal player_initialized(player_node: Node2D)

var _player_node: Node2D
var _movement_component: MovementComponent
var _paint_component: PaintComponent
var _ability_component: AbilityComponent
var _input_component: InputComponent
var _health_component: HealthComponent
var _hitbox: Area2D

func initialize(player_node: Node2D) -> void:
	_player_node = player_node
	if not is_instance_valid(_player_node):
		push_error("PlayerService: Provided player node is invalid.")
		return

	_movement_component = _player_node.find_child(&"MovementComponent", true, false)
	if not is_instance_valid(_movement_component):
		push_error("PlayerService: Failed to find MovementComponent.")

	_paint_component = _player_node.find_child(&"PaintComponent", true, false)
	if not is_instance_valid(_paint_component):
		push_error("PlayerService: Failed to find PaintComponent.")

	_ability_component = _player_node.find_child(&"AbilityComponent", true, false)
	if not is_instance_valid(_ability_component):
		push_error("PlayerService: Failed to find AbilityComponent.")

	_input_component = _player_node.find_child(&"InputComponent", true, false)
	if not is_instance_valid(_input_component):
		push_error("PlayerService: Failed to find InputComponent.")

	_health_component = _player_node.find_child(&"HealthComponent", true, false)
	if not is_instance_valid(_health_component):
		push_error("PlayerService: Failed to find HealthComponent.")

	_hitbox = _player_node.find_child(&"Hitbox", true, false)
	if not is_instance_valid(_hitbox):
		push_error("PlayerService: Failed to find Hitbox.")

	player_initialized.emit(_player_node)

func get_player_node() -> Node2D:
	return _player_node

func get_movement_component() -> MovementComponent:
	return _movement_component

func get_paint_component() -> PaintComponent:
	return _paint_component

func get_ability_component() -> AbilityComponent:
	return _ability_component

func get_input_component() -> InputComponent:
	return _input_component

func get_health_component() -> HealthComponent:
	return _health_component

func get_hitbox() -> Area2D:
	return _hitbox
