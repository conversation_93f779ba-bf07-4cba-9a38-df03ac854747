class_name InvertedControlsSystem
extends Node

@export var player_service: PlayerService

var _player_ability_component: AbilityComponent
var _player_movement_component: MovementComponent
var _is_player_moving: bool = false

func _ready() -> void:
	player_service.player_initialized.connect(_on_player_initialized)

func _on_player_initialized(_player_node: Node2D) -> void:
	_player_ability_component = player_service.get_ability_component()
	_player_movement_component = player_service.get_movement_component()

	if is_instance_valid(_player_movement_component) and is_instance_valid(_player_movement_component.data):
		_player_movement_component.data.movement_started.connect(_on_player_movement_started)
		_player_movement_component.data.movement_completed.connect(_on_player_movement_completed)

func _process(delta: float) -> void:
	if not is_instance_valid(_player_ability_component):
		return

	for ability: AbilityData in _player_ability_component.abilities:
		if ability is InvertedControlsAbilityData:
			var inverted_ability: InvertedControlsAbilityData = ability as InvertedControlsAbilityData

			if inverted_ability.remaining_time == 0.0 and inverted_ability.duration > 0.0:
				inverted_ability.initialize()

			var effective_delta: float = delta
			if _is_player_moving:
				effective_delta *= inverted_ability.time_scale_on_move

			inverted_ability.remaining_time -= effective_delta

			if inverted_ability.remaining_time <= 0.0:
				_player_ability_component.abilities.erase(inverted_ability)

			break

func _on_player_movement_started(_direction: Vector2) -> void:
	_is_player_moving = true

func _on_player_movement_completed() -> void:
	_is_player_moving = false
